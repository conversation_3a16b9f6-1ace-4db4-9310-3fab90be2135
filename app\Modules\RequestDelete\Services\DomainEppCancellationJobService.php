<?php

namespace App\Modules\RequestDelete\Services;

use App\Modules\Epp\Services\EppDomainService;
use App\Modules\Notification\Services\NotificationService;
use App\Util\Constant\UserDomainStatus;
use App\Util\Constant\DomainStatus;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Modules\CustomLogger\Services\AuthLogger;

class DomainEppCancellationJobService
{
    public static function instance(): DomainEppCancellationJobService
    {
        return new self;
    }

    public function eppDelete(array $domain): void
    {
        $eppInfoResponse = EppDomainService::instance()->callEppDomainInfo($domain['domainName']);
        $datastoreInfoResponse = EppDomainService::instance()->callDatastoreDomainInfo($domain['domainName']);

        if (!isset($eppInfoResponse['data']) || !isset($datastoreInfoResponse['data']) ||
            is_null($eppInfoResponse['data']) || is_null($datastoreInfoResponse['data'])) {
            app(AuthLogger::class)->error("Domain {$domain['domainName']} not found in EPP or datastore. EPP Response: " . json_encode($eppInfoResponse) . ", Datastore Response: " . json_encode($datastoreInfoResponse));
            NotificationService::sendFailedDeleteNotif($domain['domainName']);
            $this->revertDomainDeletion($domain);
            $this->dispatchDomainHistory($domain, 'failed');
            throw new \Exception("Domain {$domain['domainName']} not found in EPP or datastore");
        }

        $eppInfo = $eppInfoResponse['data'];
        $datastoreInfo = $datastoreInfoResponse['data'];

        if (!is_array($eppInfo) || !isset($eppInfo['status']) || !is_array($eppInfo['status'])) {
            app(AuthLogger::class)->error("Invalid EPP info structure for domain {$domain['domainName']}. EPP Info: " . json_encode($eppInfo));
            NotificationService::sendFailedDeleteNotif($domain['domainName']);
            $this->revertDomainDeletion($domain);
            $this->dispatchDomainHistory($domain, 'failed');
            throw new \Exception("Invalid EPP info structure for domain {$domain['domainName']}");
        }

        if (in_array('clientDeleteProhibited', $eppInfo['status'])) {
            app(AuthLogger::class)->info("Domain {$domain['domainName']} has clientDeleteProhibited status, cannot delete");
            NotificationService::sendFailedDeleteNotif($domain['domainName']);
            $this->revertDomainDeletion($domain);
            $this->dispatchDomainHistory($domain, 'failed');
            throw new \Exception("Domain {$domain['domainName']} has clientDeleteProhibited status");
        }

        $eppDeleteResult = EppDomainService::instance()->callEppDomainDelete($domain['domainName']);
        $datastoreDeleteResult = EppDomainService::instance()->callDatastoreDomainDelete($domain['domainName']);

        $eppSuccess = isset($eppDeleteResult['status']) && $eppDeleteResult['status'] === 'OK';
        $datastoreSuccess = isset($datastoreDeleteResult['status']) && $datastoreDeleteResult['status'] === 'OK';

        if ($eppSuccess && $datastoreSuccess) {
            $this->updateLocalDatabase($domain);
            $this->dispatchDomainHistory($domain, 'success');
        } else {
            app(AuthLogger::class)->error("EPP deletion failed for domain {$domain['domainName']}. EPP: " . json_encode($eppDeleteResult) . ", Datastore: " . json_encode($datastoreDeleteResult));
            NotificationService::sendFailedDeleteNotif($domain['domainName']);
            $this->revertDomainDeletion($domain);
            $this->dispatchDomainHistory($domain, 'failed');
            throw new \Exception("EPP deletion failed for domain {$domain['domainName']}");
        }
    }

    private function updateLocalDatabase(array $domain): void
    {
        $timestamp = now();

        $updates = [
            'status'     => UserDomainStatus::DELETED,
            'deleted_at' => $timestamp,
            'updated_at' => $timestamp,
        ];

        DB::client()->table('registered_domains')
            ->where('domain_id', $domain['domainId'])
            ->update($updates);

        DB::client()->table('domains')
            ->where('id', $domain['domainId'])
            ->update($updates);

        $this->updateDomainCancellationRequest($domain);

        DB::client()->table('pending_domain_deletions')->insert([
            'registered_domain_id' => $domain['domainId'],
            'deleted_by' => $domain['userEmail'],
            'deleted_at' => $timestamp,
            'created_at' => $timestamp,
            'updated_at' => $timestamp,
        ]);
    }

    private function updateDomainCancellationRequest(array $domain): void
    {
        $exists = DB::client()
            ->table('domain_cancellation_requests')
            ->where('domain_id', $domain['domainId'])
            ->exists();

        if (!$exists) {
            $this->createDomainCancellationRequest($domain);
            return;
        }

        $date = Carbon::parse($domain['createdDate']);
        // Domain is refundable if it was created within the last 5 days
        $is_refunded = $date->greaterThanOrEqualTo(now()->subDays(5)) ? true : false;

        $adminId = $domain['adminId'] ?? 1;
        $adminName = $domain['adminName'] ?? 'System';
        $adminEmail = $domain['adminEmail'] ?? '<EMAIL>';
        $adminFullName = "{$adminName} ({$adminEmail})";
        $supportNote = $domain['supportNote'] ?? "{$adminFullName} deleted domain: {$domain['domainName']}";

        DB::client()->table('domain_cancellation_requests')
            ->where('domain_id', $domain['domainId'])
            ->update([
                'support_agent_id'   => $adminId,
                'support_agent_name' => $adminFullName,
                'deleted_at'         => now(),
                'feedback_date'      => now(),
                'support_note'       => $supportNote,
                'is_refunded'        => $is_refunded,
            ]);
    }

    private function createDomainCancellationRequest(array $domain): void
    {
        $date = Carbon::parse($domain['createdDate']);
        // Domain is refundable if it was created within the last 5 days
        $is_refunded = $date->greaterThanOrEqualTo(now()->subDays(5)) ? true : false;

        $adminId = $domain['adminId'] ?? 1;
        $adminName = $domain['adminName'] ?? 'System';
        $adminEmail = $domain['adminEmail'] ?? '<EMAIL>';
        $adminFullName = "{$adminName} ({$adminEmail})";
        $supportNote = $domain['supportNote'] ?? "{$adminFullName} deleted domain: {$domain['domainName']}";

        DB::client()->table('domain_cancellation_requests')->insert([
            'user_id'             => $domain['userId'],
            'domain_id'           => $domain['domainId'],
            'reason'              => $domain['reason'],
            'support_agent_id'    => $adminId,
            'support_agent_name'  => $adminFullName,
            'deleted_at'          => now(),
            'feedback_date'       => now(),
            'support_note'        => $supportNote,
            'is_refunded'         => $is_refunded,
            'requested_at'        => now(),
        ]);
    }

    private function dispatchDomainHistory(array $domain, string $status): void
    {
        $adminId = $domain['adminId'] ?? 1;
        $adminName = $domain['adminName'] ?? 'System';
        $adminEmail = $domain['adminEmail'] ?? '<EMAIL>';

        if ($status === 'success') {
            $message = 'Domain "' . $domain['domainName'] . '" deleted by ' . $adminName ;
        } else {
            $message = 'Domain "' . $domain['domainName'] . '" deletion failed by ' . $adminName;
        }

        DB::client()->table('domain_transaction_histories')->insert([
            'domain_id' => $domain['domainId'],
            'type'      => 'DOMAIN_DELETED',
            'user_id'   => $domain['userId'],
            'status'    => $status,
            'message'   => $message,
            'payload'   => json_encode($domain),
            'created_at'=> now(),
            'updated_at'=> now(),
        ]);
    }

    private function revertDomainDeletion(array $domain): void
    {
        $timestamp = now();

        DB::client()->table('domains')
            ->where('id', $domain['domainId'])
            ->update([
                'status' => DomainStatus::ACTIVE,
                'updated_at' => $timestamp,
            ]);

        DB::client()->table('registered_domains')
            ->where('domain_id', $domain['domainId'])
            ->update([
                'status' => UserDomainStatus::OWNED,
                'updated_at' => $timestamp,
            ]);

        DB::client()->table('domain_cancellation_requests')
            ->where('domain_id', $domain['domainId'])
            ->update([
                'support_agent_id' => null,
                'support_agent_name' => null,
                'feedback_date' => null,
                'support_note' => 'Deletion request reverted due to EPP failure: ' . ($domain['domainName'] ?? 'Unknown domain'),
                'deleted_at' => null, 
            ]);

        $this->sendReversionNotification($domain);
    }

    private function sendReversionNotification(array $domain): void
    {
        if (!isset($domain['userId']) || !isset($domain['domainName'])) {
            return;
        }

        $message = 'Your domain deletion request for "' . $domain['domainName'] . '" could not be processed due to technical issues. The domain has been reactivated and is now available for use. Please contact support if you still wish to delete this domain.';

        DB::client()->table('notifications')->insert([
            'user_id' => $domain['userId'],
            'title' => 'Domain Deletion Request Failed',
            'message' => $message,
            'redirect_url' => '/domain',
            'created_at' => now(),
            'updated_at' => now(),
            'importance' => 'important',
        ]);
    }
}
