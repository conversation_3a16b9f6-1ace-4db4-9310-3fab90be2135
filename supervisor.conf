[program:admin-email]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php %(here)s/artisan queue:work email_jobs --queue=default --tries=0 --timeout=120 --max-time=900 --stop-when-empty
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=%(here)s/storage/logs/worker.log
startsecs=0
stopwaitsecs=180

[program:admin-guest-request]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php %(here)s/artisan queue:work guest_request_jobs --queue=default,ACCEPT,DENIED,DELETED --tries=0 --timeout=120 --max-time=900 --stop-when-empty
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=%(here)s/storage/logs/worker.log
startsecs=0
stopwaitsecs=180

[program:admin-domain-deletion]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php %(here)s/artisan queue:work domain_deletion_jobs --queue=VERISIGN-DELETE,PIR-DELETE --tries=3 --timeout=120 --max-time=900 --stop-when-empty
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=%(here)s/storage/logs/worker.log
startsecs=0
stopwaitsecs=180

[program:admin-domain-client-hold]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php %(here)s/artisan queue:work domain_client_hold_jobs --queue=VERISIGN-CLIENT-HOLD,PIR-CLIENT-HOLD --tries=3 --timeout=120 --max-time=900 --stop-when-empty
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=%(here)s/storage/logs/worker.log
startsecs=0
stopwaitsecs=180

[program:admin-domain-cancellation]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php %(here)s/artisan queue:work domain_cancellation_jobs --queue=VERISIGN-CANCELLATION,PIR-CANCELLATION --tries=3 --timeout=120 --max-time=900 --stop-when-empty
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=%(here)s/storage/logs/worker.log
startsecs=0
stopwaitsecs=180


