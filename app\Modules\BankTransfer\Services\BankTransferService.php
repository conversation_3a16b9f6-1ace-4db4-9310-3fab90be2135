<?php

namespace App\Modules\BankTransfer\Services;

use App\Exceptions\FailedRequestException;
use App\Events\AdminActionEvent;
use App\Modules\AdminHistory\Constants\HistoryType;
use App\Modules\BankTransfer\Constants\BankTransferPurposeConstants;
use App\Modules\BillingClient\Services\MarketInvoiceService;
use App\Traits\CursorPaginate;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class BankTransferService
{
    use CursorPaginate;

    private $pageLimit = 20;

    private const VERIFY = 'verify';

    private const REVERSE = 'reverse';

    private const UNVERIFIED = 'unverified';

    public static function instance(): self
    {
        $bankTransferService = new self;

        return $bankTransferService;
    }

    public function getIndexData($request)
    {
        $this->pageLimit = $request->input('limit', 20);
        $builder = $this->query()->paginate($this->pageLimit)->appends($request->all());
        // dd($builder);

        return CursorPaginate::cursor($builder, $this->paramToURI($request));
    }

    public function query()
    {
        $query = DB::client()->table('payment_services')
            ->join('bank_transfers', 'bank_transfers.id', '=', 'payment_services.bank_transfer_id')
            ->join('users', 'users.id', '=', 'payment_services.user_id')
            ->whereNull('payment_services.account_credit_id')
            ->select(
                'bank_transfers.*',
                'bank_transfers.verified_at as status',
                'bank_transfers.gross_amount as amount',
                'payment_services.id as payment_service_id',
                'users.id as user_id',
                'users.first_name',
                'users.last_name',
                'users.email',
            );

        return $this->applyFilters($query);
    }

    private function applyFilters($query)
    {
        $query = $this->applyStatusFilter($query);
        $query = $this->applyOrderByFilter($query);
        $query = $this->applyNameFilter($query);
        $query = $this->applyCompanyFilter($query);
        return $query;
    }

    private function applyCompanyFilter($query) {
        if(request()->has('company')){
            $query->where('company','like',request()->company.'%');
        }
        return $query;
    }

    private function applyNameFilter($query)
    {
        if (request()->has('name')) {
            $query->where('account_name', 'like',request()->name .'%');
        }
        return $query;
    }

    private function applyStatusFilter($query)
    {
        if (request()->has('status')) {
            $status = request()->status;

            switch ($status) {
                case 'pending':
                    $query->whereNull('bank_transfers.verified_at')->whereNull('bank_transfers.reviewed_at')->whereNull('bank_transfers.deleted_at');
                    break;
                case 'verified':
                    $query->whereNotNull('bank_transfers.verified_at');
                    break;
                case 'unverified':
                    $query->whereNotNull('bank_transfers.reviewed_at');
                    break;
                case 'rejected':
                    $query->whereNotNull('bank_transfers.deleted_at');
                    break;
            }
        }
        return $query;
    }

    private function applyOrderByFilter($query)
    {
        if (request()->has('orderby')) {
            $orderBy = request()->orderby;
            switch ($orderBy) {
                case 'created_at:desc':
                    $query->orderBy('bank_transfers.created_at', 'desc');
                    break;
                case 'created_at:asc':
                    $query->orderBy('bank_transfers.created_at', 'asc');
                    break;
                case 'amount:desc':
                    $query->orderBy('bank_transfers.gross_amount', 'desc');
                    break;
                case 'amount:asc':
                    $query->orderBy('bank_transfers.gross_amount', 'asc');
                    break;
                case 'updated_at:desc':
                    $query->orderBy('bank_transfers.updated_at', 'desc');
                    break;
                case 'updated_at:asc':
                    $query->orderBy('bank_transfers.updated_at', 'asc');
                    break;
                case 'received_amount:desc':
                    $query->orderBy('bank_transfers.net_amount', 'desc');
                    break;
                case 'received_amount:asc':
                    $query->orderBy('bank_transfers.net_amount', 'asc');
                    break;
                case 'name:asc':
                    $query->orderBy('bank_transfers.account_name', 'asc');
                    break;
                case 'name:desc':
                    $query->orderBy('bank_transfers.account_name', 'desc');
                    break;
                default:
                    $query->orderBy('bank_transfers.id', 'desc');
                    break;
            }
        } else {
            $query->orderBy('bank_transfers.id', 'desc');
        }

        return $query;
    }

    public function getAll()
    {
        return $this->query()->get()->all();
    }

    public function getVerifyData(int $id)
    {
        $item = $this->query()->where('bank_transfers.id', $id)->get()->first();

        if ($item->verified_at != null) {
            throw new FailedRequestException(400, 'Bad request.', 'Cannot verify transaction.');
        }

        if ($item->purpose == BankTransferPurposeConstants::MARKETPLACE_PAYMENT) {
            $marketPlaceInvoiceId = DB::client()->table('market_place_payment_invoices')
                ->where('payment_service_id', '=', $item->payment_service_id)
                ->first()
                ->id;

            $marketPlaceInvoice = MarketInvoiceService::instance()->getMarketPlaceInvoice($marketPlaceInvoiceId, $item->user_id);

            return [
                'payment' => $item,
                'action' => self::VERIFY,
                'purpose' => $item->purpose,
                'invoiceOrders' => $marketPlaceInvoice
            ];
        }

        return [
            'item' => $item,
            'action' => self::VERIFY,
            'purpose' => $item->purpose
        ];
    }

    public function verify(array $request)
    {
        $bankTransfer = $this->query()->where('bank_transfers.id', $request['bank_transfer_id'])->get()->first();

        if (
            $bankTransfer->verified_at != null ||
            $bankTransfer->credited_at != null
        ) {
            throw new FailedRequestException(400, 'Bad request.', 'Cannot verify transaction.');
        }

        $netAmount = floatval($request['net_amount']);
        $this->validateNetAmount($bankTransfer->gross_amount, $netAmount);
        $serviceFee = $bankTransfer->gross_amount - $netAmount;
        $verifiedAt = $request['is_verified'] ? now() : null;

        $data = [
            'net_amount' => $netAmount ?? $bankTransfer->gross_amount,
            'service_fee' => $serviceFee ?? 0,
            'note' => $request['note'] ?? '',
            'verified_at' => $verifiedAt,
            'reviewed_at' => null,
            'retrieved_at' => null,
            'admin_id' => Auth::id(),
            'updated_at' => now(),
        ];

        DB::client()->table('bank_transfers')->where('id', $request['bank_transfer_id'])->update($data);

        $message = "Bank transfer id: {$request['bank_transfer_id']} verified for user {$bankTransfer->email} - Amount: $" . number_format($netAmount, 2) . " by " . Auth::user()->email;
        event(new AdminActionEvent(Auth::id(), HistoryType::BANK_TRANSFER_VERIFIED, $message));
    }


    public function reject(array $request)
    {
        $bankTransfer = $this->query()->where('bank_transfers.id', $request['bank_transfer_id'])->get()->first();

        if (
            $bankTransfer->credited_at != null ||
            $bankTransfer->deleted_at != null
        ) {
            throw new FailedRequestException(400, 'Bad request.', 'Cannot reject transaction.');
        }

        $netAmount = floatval($request['net_amount']);
        $serviceFee = $bankTransfer->gross_amount - $netAmount;
        $isFirstAttempt = $bankTransfer->reviewed_at === null && $bankTransfer->deleted_at === null;

        $data = [
            'net_amount' => $netAmount ?? 0,
            'service_fee' => $serviceFee ?? 0,
            'note' => $request['note'] ?? '',
            'reviewed_at' => $isFirstAttempt ? now() : null,
            'deleted_at' => $isFirstAttempt ? null : now(),
            'retrieved_at' => null,
            'admin_id' => Auth::id(),

        ];

        DB::client()->table('bank_transfers')->where('id', $request['bank_transfer_id'])->update($data);

        $this->notifyBankTransferReject($bankTransfer->user_id, $isFirstAttempt);

        $action = $isFirstAttempt ? 'rejected' : 'permanently rejected';
        $message = "Bank transfer id: {$request['bank_transfer_id']} {$action} for user {$bankTransfer->email} by " . Auth::user()->email;
        event(new AdminActionEvent(Auth::id(), HistoryType::BANK_TRANSFER_REJECTED, $message));
    }

    // PRIVATE FUNCTIONS
    private function paramToURI($request)
    {
        $params = [];

        if ($request->has('orderby')) {
            $params[] = 'orderby=' . $request->orderby;
        }

        if ($request->has('status')) {
            $params[] = 'status=' . $request->status;
        }

        return $params;
    }

    private function notifyBankTransferReject(int $userId, bool $isFirstAttempt)
    {
        if ($isFirstAttempt) {
            $this->notify($userId, 'Bank Transfer Unverified', 'Your bank transfer request has been Unverified. Please contact support.', 'important');
        } else {
            $this->notify($userId, 'Bank Transfer Rejected', 'Your bank transfer request has been rejected.', 'critical');
        }
    }

    private function notify(int $userId, string $title, string $message, string $importance)
    {
        $data = [
            'user_id' => $userId,
            'title' => $title,
            'message' => $message,
            'importance' => $importance,
            'redirect_url' => '/wire-transfer',
            'read_at' => null,
            'created_at' => now(),
            'updated_at' => now(),
        ];

        DB::client()->table('notifications')->insert($data);
    }

    private function validateNetAmount(float $grossAmount, float $netAmount)
    {
        if ($netAmount <= 0) {
            throw new FailedRequestException(400, 'Bad request.', 'Net amount cannot be zero.');
        }

        if ($netAmount > $grossAmount) {
            throw new FailedRequestException(400, 'Bad request.', 'Net amount cannot be greater than gross amount.');
        }

        $threshold = $grossAmount * 0.2;

        if ($netAmount < $threshold) {
            throw new FailedRequestException(400, 'Bad request.', 'Net amount cannot be less than 20% of gross amount.');
        }
    }
}
