<?php

namespace App\Modules\RequestDelete\Services;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Epp\Services\EppDomainService;
use App\Modules\Notification\Services\NotificationService;
use App\Events\DomainHistoryEvent;
use Exception;

class DomainRequestDeleteJobService
{
    use UserLoggerTrait;

    private static ?self $instance = null;

    private function __construct()
    {
    }

    public static function instance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function processDeleteRequest(array $domain): void
    {
        try {
            $eppInfoResponse = EppDomainService::instance()->callEppDomainInfo($domain['domainName']);
            $datastoreInfoResponse = EppDomainService::instance()->callDatastoreDomainInfo($domain['domainName']);

            if ($this->isDomainNotFound($eppInfoResponse)) {
                app(AuthLogger::class)->error("Domain {$domain['domainName']} not found in EPP. Cannot proceed with clientDeleteProhibited removal.");
                return;
            }

            if ($this->isDomainNotFound($datastoreInfoResponse)) {
                app(AuthLogger::class)->error("Domain {$domain['domainName']} not found in Datastore. Cannot proceed with clientDeleteProhibited removal.");
                return;
            }

            $updatePayload = [
                'name' => $domain['domainName'],
                'statusRemove' => ['clientDeleteProhibited']
            ];

            EppDomainService::instance()->updateEppDomain($updatePayload);
            event(new DomainHistoryEvent(['domain_id' => $domain['domainId'],'type' => 'DOMAIN_UPDATED','status' => 'success','user_id' => $domain['userId'] ?? null ,'message' => 'Domain deletion request approved','payload' => json_encode($domain),]));

        } catch (Exception $e) {
            app(AuthLogger::class)->error("Exception occurred while processing domain request delete for {$domain['domainName']}: " . $e->getMessage());
            throw $e;
        }
    }

    private function isDomainNotFound(array $response): bool
    {
        return isset($response['status']) && $response['status'] === 'error' && 
               (isset($response['message']) && stripos($response['message'], 'no domain exist found') !== false);
    }

}
