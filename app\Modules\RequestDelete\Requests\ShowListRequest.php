<?php

namespace App\Modules\RequestDelete\Requests;

// use App\Modules\PendingDelete\Constants\StatusTypes;

use App\Modules\RequestDelete\Constants\StatusTypes;
use App\Modules\RequestDelete\Services\DatabaseQueryService;
use App\Modules\RequestDelete\Services\DomainDeleteService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ShowListRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    { 
        if ($this->routeIs('domain.support-feedback.store')) {
            return [
                'support_note' => ['required', 'string', 'min:10'],
            ];
        }

        return [
            'statusType' => ['string', Rule::in(StatusTypes::TYPES)],
            'orderby' => ['string', Rule::in([
                'domain:desc',
                'domain:asc',
                'requested_at:desc',
                'requested_at:asc',
            ])],
            'domain' => ['string'],
            'email' => ['email', 'max:255'],
            'deletedBy' => ['string'],
            'support_note' => ['string', 'min:10'],
        ];
    }

    public function show()
    {
        return DatabaseQueryService::instance()->get($this);
    }

    public function feedbackSave()
    {
        return DatabaseQueryService::instance()->supportNoteSave($this);
    }

    public function approve_deleteRequest() {
        return DomainDeleteService::instance()->approveDeleteRequest($this);
    }

    public function reject_deleteRequest() {
        return DomainDeleteService::instance()->rejectDeleteRequest($this);
    }

    public function cancel_deleteRequest() {
        return DomainDeleteService::instance()->cancelDeleteRequest($this);
    }
}
